# استخدام Python 3.11 كصورة أساسية
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        gettext \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات وتثبيتها
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات المشروع
COPY . /app/

# إنشاء مستخدم غير جذر
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# تجميع الملفات الثابتة
RUN python manage.py collectstatic --noinput

# فتح المنفذ
EXPOSE 8000

# تشغيل الخادم
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "hotel_management.wsgi:application"]
