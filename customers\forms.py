from django import forms
from .models import Customer

class CustomerForm(forms.ModelForm):
    """نموذج إضافة/تعديل العميل"""
    class Meta:
        model = Customer
        fields = [
            'first_name', 'last_name', 'email', 'phone', 'gender', 'date_of_birth',
            'id_type', 'id_number', 'nationality', 'address', 'emergency_contact_name',
            'emergency_contact_phone', 'is_vip', 'notes'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم العائلة'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+966501234567'
            }),
            'gender': forms.Select(attrs={
                'class': 'form-control'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'id_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'id_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهوية'
            }),
            'nationality': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الجنسية'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'العنوان الكامل'
            }),
            'emergency_contact_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم جهة الاتصال الطارئ'
            }),
            'emergency_contact_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+966501234567'
            }),
            'is_vip': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
        }
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'email': 'البريد الإلكتروني',
            'phone': 'رقم الهاتف',
            'gender': 'الجنس',
            'date_of_birth': 'تاريخ الميلاد',
            'id_type': 'نوع الهوية',
            'id_number': 'رقم الهوية',
            'nationality': 'الجنسية',
            'address': 'العنوان',
            'emergency_contact_name': 'اسم جهة الاتصال الطارئ',
            'emergency_contact_phone': 'هاتف جهة الاتصال الطارئ',
            'is_vip': 'عميل مميز',
            'notes': 'ملاحظات',
        }

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            # التحقق من عدم وجود بريد إلكتروني مكرر
            existing_customer = Customer.objects.filter(email=email)
            if self.instance.pk:
                existing_customer = existing_customer.exclude(pk=self.instance.pk)
            if existing_customer.exists():
                raise forms.ValidationError('هذا البريد الإلكتروني مستخدم بالفعل')
        return email

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone:
            # إزالة المسافات والرموز غير الضرورية
            phone = ''.join(filter(str.isdigit, phone.replace('+', '+')))
            if not phone.startswith('+'):
                if phone.startswith('966'):
                    phone = '+' + phone
                elif phone.startswith('05'):
                    phone = '+966' + phone[1:]
                else:
                    phone = '+966' + phone
        return phone

    def clean(self):
        cleaned_data = super().clean()
        id_type = cleaned_data.get('id_type')
        id_number = cleaned_data.get('id_number')
        
        if id_type and id_number:
            # التحقق من عدم وجود هوية مكررة
            existing_customer = Customer.objects.filter(
                id_type=id_type, 
                id_number=id_number
            )
            if self.instance.pk:
                existing_customer = existing_customer.exclude(pk=self.instance.pk)
            if existing_customer.exists():
                raise forms.ValidationError('هذه الهوية مسجلة بالفعل في النظام')
        
        return cleaned_data

class CustomerSearchForm(forms.Form):
    """نموذج البحث في العملاء"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم، البريد الإلكتروني، أو رقم الهاتف...'
        }),
        label='البحث'
    )
    nationality = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'الجنسية'
        }),
        label='الجنسية'
    )
    gender = forms.ChoiceField(
        choices=[('', 'الكل')] + Customer.GENDER_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='الجنس'
    )
    is_vip = forms.ChoiceField(
        choices=[('', 'الكل'), ('true', 'مميز'), ('false', 'عادي')],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='نوع العميل'
    )
    id_type = forms.ChoiceField(
        choices=[('', 'جميع الأنواع')] + Customer.ID_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='نوع الهوية'
    )
