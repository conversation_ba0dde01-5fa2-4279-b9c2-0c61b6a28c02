from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    """نموذج المستخدم المخصص"""
    USER_TYPES = (
        ('admin', 'مدير'),
        ('receptionist', 'موظف استقبال'),
        ('manager', 'مدير عام'),
    )

    user_type = models.CharField(
        max_length=20,
        choices=USER_TYPES,
        default='receptionist',
        verbose_name='نوع المستخدم'
    )
    phone = models.Char<PERSON>ield(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='رقم الهاتف'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'

    def __str__(self):
        return f"{self.username} - {self.get_user_type_display()}"
