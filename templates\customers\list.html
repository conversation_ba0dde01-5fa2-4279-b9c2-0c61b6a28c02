{% extends 'base.html' %}

{% block title %}قائمة العملاء - نظام إدارة الفندق{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>قائمة العملاء</h2>
        <a href="{% url 'customers:add' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة عميل جديد
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>الجنسية</th>
                            <th>عميل مميز</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td>{{ customer.full_name }}</td>
                            <td>{{ customer.email }}</td>
                            <td>{{ customer.phone }}</td>
                            <td>{{ customer.nationality }}</td>
                            <td>
                                {% if customer.is_vip %}
                                    <span class="badge bg-warning">مميز</span>
                                {% else %}
                                    <span class="badge bg-secondary">عادي</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'customers:detail' customer.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'customers:edit' customer.pk %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'customers:delete' customer.pk %}" class="btn btn-sm btn-danger delete-confirm" data-item-name="العميل {{ customer.full_name }}">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">لا يوجد عملاء</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
