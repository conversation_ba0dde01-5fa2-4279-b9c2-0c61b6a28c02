{% extends 'base.html' %}

{% block title %}قائمة العملاء - نظام إدارة الفندق{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>قائمة العملاء</h2>
            <p class="text-muted">إدارة عملاء الفندق وبياناتهم</p>
        </div>
        <a href="{% url 'customers:add' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة عميل جديد
        </a>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ stats.total }}</div>
                        <div class="stats-label">إجمالي العملاء</div>
                    </div>
                    <div class="icon-md">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card warning">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ stats.vip }}</div>
                        <div class="stats-label">عملاء مميزون</div>
                    </div>
                    <div class="icon-md">
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card info">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ stats.regular }}</div>
                        <div class="stats-label">عملاء عاديون</div>
                    </div>
                    <div class="icon-md">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card success">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ stats.with_bookings }}</div>
                        <div class="stats-label">لديهم حجوزات</div>
                    </div>
                    <div class="icon-md">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج البحث والتصفية -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>
                البحث والتصفية
            </h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    {{ search_form.search }}
                </div>
                <div class="col-md-2">
                    {{ search_form.nationality }}
                </div>
                <div class="col-md-2">
                    {{ search_form.gender }}
                </div>
                <div class="col-md-2">
                    {{ search_form.is_vip }}
                </div>
                <div class="col-md-1">
                    {{ search_form.id_type }}
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول العملاء -->
    <div class="card">
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>الجنسية</th>
                                <th>نوع العميل</th>
                                <th>الحجوزات</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in page_obj %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            {{ customer.first_name|first }}{{ customer.last_name|first }}
                                        </div>
                                        <div>
                                            <strong>{{ customer.full_name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ customer.get_gender_display }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                        {{ customer.email }}
                                    </a>
                                </td>
                                <td>
                                    <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                        {{ customer.phone }}
                                    </a>
                                </td>
                                <td>{{ customer.nationality }}</td>
                                <td>
                                    {% if customer.is_vip %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-star me-1"></i>
                                            مميز
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">عادي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="text-center">
                                        <span class="badge bg-primary">{{ customer.total_bookings }}</span>
                                        {% if customer.active_bookings > 0 %}
                                            <br>
                                            <small class="text-success">
                                                <i class="fas fa-circle me-1"></i>
                                                {{ customer.active_bookings }} نشط
                                            </small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>{{ customer.created_at|date:"d/m/Y" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'customers:detail' customer.pk %}"
                                           class="btn btn-sm btn-outline-info"
                                           title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'customers:edit' customer.pk %}"
                                           class="btn btn-sm btn-outline-warning"
                                           title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'customers:delete' customer.pk %}"
                                           class="btn btn-sm btn-outline-danger delete-confirm"
                                           data-item-name="العميل {{ customer.full_name }}"
                                           title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- ترقيم الصفحات -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="ترقيم الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد عملاء</h5>
                    <p class="text-muted">ابدأ بإضافة عملاء جدد للفندق</p>
                    <a href="{% url 'customers:add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عميل جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>
{% endblock %}
