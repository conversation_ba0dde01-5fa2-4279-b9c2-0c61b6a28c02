// JavaScript الأساسي لتطبيق إدارة الفندق

$(document).ready(function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تفعيل popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // تأكيد الحذف
    $('.delete-confirm').on('click', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        var itemName = $(this).data('item-name') || 'هذا العنصر';
        
        if (confirm('هل أنت متأكد من حذف ' + itemName + '؟')) {
            window.location.href = url;
        }
    });

    // تحديث حالة الغرفة
    $('.room-status-update').on('change', function() {
        var roomId = $(this).data('room-id');
        var newStatus = $(this).val();
        
        $.ajax({
            url: '/rooms/update-status/',
            method: 'POST',
            data: {
                'room_id': roomId,
                'status': newStatus,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    showAlert('تم تحديث حالة الغرفة بنجاح', 'success');
                } else {
                    showAlert('حدث خطأ في تحديث حالة الغرفة', 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ في الاتصال', 'danger');
            }
        });
    });

    // البحث المباشر
    $('#search-input').on('keyup', function() {
        var searchTerm = $(this).val().toLowerCase();
        $('.searchable-item').each(function() {
            var text = $(this).text().toLowerCase();
            if (text.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // تصفية الجدول
    $('.table-filter').on('change', function() {
        var filterValue = $(this).val();
        var filterColumn = $(this).data('filter-column');
        
        $('.filterable-row').each(function() {
            var cellValue = $(this).find('td').eq(filterColumn).text();
            if (filterValue === '' || cellValue === filterValue) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // تحديث التاريخ والوقت
    function updateDateTime() {
        var now = new Date();
        var options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        };
        var dateTimeString = now.toLocaleDateString('ar-SA', options);
        $('#current-datetime').text(dateTimeString);
    }

    // تحديث التاريخ كل ثانية
    setInterval(updateDateTime, 1000);
    updateDateTime();

    // تحقق من صحة النماذج
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });

    // تحسين تجربة المستخدم للنماذج
    $('.form-control').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        if ($(this).val() === '') {
            $(this).parent().removeClass('focused');
        }
    });

    // تحديد الكل/إلغاء تحديد الكل
    $('#select-all').on('change', function() {
        $('.item-checkbox').prop('checked', $(this).prop('checked'));
        updateBulkActions();
    });

    $('.item-checkbox').on('change', function() {
        updateBulkActions();
    });

    function updateBulkActions() {
        var checkedCount = $('.item-checkbox:checked').length;
        if (checkedCount > 0) {
            $('.bulk-actions').show();
            $('.bulk-count').text(checkedCount);
        } else {
            $('.bulk-actions').hide();
        }
    }

    // إجراءات مجمعة
    $('.bulk-action-btn').on('click', function() {
        var action = $(this).data('action');
        var selectedIds = [];
        
        $('.item-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            showAlert('يرجى تحديد عنصر واحد على الأقل', 'warning');
            return;
        }

        if (confirm('هل أنت متأكد من تنفيذ هذا الإجراء على العناصر المحددة؟')) {
            $.ajax({
                url: '/bulk-action/',
                method: 'POST',
                data: {
                    'action': action,
                    'ids': selectedIds,
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('تم تنفيذ الإجراء بنجاح', 'success');
                        location.reload();
                    } else {
                        showAlert('حدث خطأ في تنفيذ الإجراء', 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ في الاتصال', 'danger');
                }
            });
        }
    });

    // تحديث الإحصائيات
    function updateStats() {
        $.ajax({
            url: '/api/stats/',
            method: 'GET',
            success: function(data) {
                $('#total-rooms').text(data.total_rooms);
                $('#available-rooms').text(data.available_rooms);
                $('#occupied-rooms').text(data.occupied_rooms);
                $('#total-bookings').text(data.total_bookings);
                $('#today-checkins').text(data.today_checkins);
                $('#today-checkouts').text(data.today_checkouts);
            }
        });
    }

    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStats, 30000);

    // تحسين الجداول
    $('.table-sortable th').on('click', function() {
        var table = $(this).closest('table');
        var index = $(this).index();
        var rows = table.find('tbody tr').toArray();
        var isAsc = $(this).hasClass('asc');

        rows.sort(function(a, b) {
            var aText = $(a).find('td').eq(index).text();
            var bText = $(b).find('td').eq(index).text();
            
            if ($.isNumeric(aText) && $.isNumeric(bText)) {
                return isAsc ? aText - bText : bText - aText;
            } else {
                return isAsc ? aText.localeCompare(bText) : bText.localeCompare(aText);
            }
        });

        table.find('tbody').empty().append(rows);
        
        $(this).removeClass('asc desc').addClass(isAsc ? 'desc' : 'asc');
        $(this).siblings().removeClass('asc desc');
    });

    // تحسين التنقل
    $('.nav-link').on('click', function() {
        $('.nav-link').removeClass('active');
        $(this).addClass('active');
    });

    // تحسين الرسائل
    $('.alert').delay(5000).fadeOut();
});

// دالة لإظهار الرسائل
function showAlert(message, type) {
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                    message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>';
    
    $('#alerts-container').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').first().fadeOut();
    }, 5000);
}

// دالة لتنسيق الأرقام
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// دالة لتنسيق التاريخ
function formatDate(date) {
    var options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    return new Date(date).toLocaleDateString('ar-SA', options);
}

// دالة لتنسيق الوقت
function formatTime(time) {
    var options = {
        hour: '2-digit',
        minute: '2-digit'
    };
    return new Date(time).toLocaleTimeString('ar-SA', options);
}

// دالة للتحقق من صحة البريد الإلكتروني
function validateEmail(email) {
    var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// دالة للتحقق من صحة رقم الهاتف
function validatePhone(phone) {
    var re = /^[\+]?[1-9][\d]{0,15}$/;
    return re.test(phone);
}

// دالة لحساب عدد الأيام بين تاريخين
function daysBetween(date1, date2) {
    var oneDay = 24 * 60 * 60 * 1000;
    var firstDate = new Date(date1);
    var secondDate = new Date(date2);
    
    return Math.round(Math.abs((firstDate - secondDate) / oneDay));
}

// دالة لتحديث حالة الصفحة
function updatePageStatus() {
    var currentPage = window.location.pathname;
    $('.nav-link[href="' + currentPage + '"]').addClass('active');
}
