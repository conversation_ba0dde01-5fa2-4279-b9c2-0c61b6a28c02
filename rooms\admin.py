from django.contrib import admin
from .models import RoomType, Room

@admin.register(RoomType)
class RoomTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'base_price', 'max_occupancy')
    search_fields = ('name',)
    list_filter = ('max_occupancy',)

@admin.register(Room)
class RoomAdmin(admin.ModelAdmin):
    list_display = ('number', 'room_type', 'floor', 'status', 'price_per_night', 'is_active')
    list_filter = ('room_type', 'floor', 'status', 'is_active')
    search_fields = ('number',)
    list_editable = ('status', 'is_active')
    ordering = ('number',)
