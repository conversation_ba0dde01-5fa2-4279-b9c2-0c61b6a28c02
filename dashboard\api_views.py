from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import date, timedelta
from rooms.models import Room, RoomType
from customers.models import Customer
from bookings.models import Booking, Payment

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_stats(request):
    """API لإحصائيات لوحة التحكم"""
    today = timezone.now().date()
    
    # إحصائيات الغرف
    room_stats = {
        'total_rooms': Room.objects.filter(is_active=True).count(),
        'available_rooms': Room.objects.filter(status='available', is_active=True).count(),
        'occupied_rooms': Room.objects.filter(status='occupied', is_active=True).count(),
        'maintenance_rooms': Room.objects.filter(status='maintenance', is_active=True).count(),
        'cleaning_rooms': Room.objects.filter(status='cleaning', is_active=True).count(),
    }
    
    # إحصائيات الحجوزات
    booking_stats = {
        'total_bookings': Booking.objects.count(),
        'today_checkins': Booking.objects.filter(
            check_in_date=today,
            status__in=['confirmed', 'checked_in']
        ).count(),
        'today_checkouts': Booking.objects.filter(
            check_out_date=today,
            status='checked_in'
        ).count(),
        'pending_bookings': Booking.objects.filter(status='pending').count(),
        'confirmed_bookings': Booking.objects.filter(status='confirmed').count(),
    }
    
    # إحصائيات العملاء
    customer_stats = {
        'total_customers': Customer.objects.count(),
        'vip_customers': Customer.objects.filter(is_vip=True).count(),
        'new_customers_this_month': Customer.objects.filter(
            created_at__month=today.month,
            created_at__year=today.year
        ).count(),
    }
    
    # الإيرادات
    this_month = timezone.now().replace(day=1)
    revenue_stats = {
        'today_revenue': Payment.objects.filter(
            created_at__date=today
        ).aggregate(total=Sum('amount'))['total'] or 0,
        'monthly_revenue': Payment.objects.filter(
            created_at__gte=this_month
        ).aggregate(total=Sum('amount'))['total'] or 0,
        'yearly_revenue': Payment.objects.filter(
            created_at__year=today.year
        ).aggregate(total=Sum('amount'))['total'] or 0,
    }
    
    # نسبة الإشغال
    occupancy_rate = 0
    if room_stats['total_rooms'] > 0:
        occupancy_rate = round(
            (room_stats['occupied_rooms'] / room_stats['total_rooms']) * 100, 1
        )
    
    return Response({
        'rooms': room_stats,
        'bookings': booking_stats,
        'customers': customer_stats,
        'revenue': revenue_stats,
        'occupancy_rate': occupancy_rate,
        'last_updated': timezone.now().isoformat(),
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def room_availability(request):
    """API لتوفر الغرف"""
    check_in = request.GET.get('check_in')
    check_out = request.GET.get('check_out')
    
    if not check_in or not check_out:
        return Response({'error': 'يجب تحديد تاريخ الوصول والمغادرة'}, status=400)
    
    try:
        check_in_date = date.fromisoformat(check_in)
        check_out_date = date.fromisoformat(check_out)
    except ValueError:
        return Response({'error': 'تنسيق التاريخ غير صحيح'}, status=400)
    
    if check_in_date >= check_out_date:
        return Response({'error': 'تاريخ المغادرة يجب أن يكون بعد تاريخ الوصول'}, status=400)
    
    # البحث عن الغرف المتاحة
    occupied_rooms = Booking.objects.filter(
        status__in=['confirmed', 'checked_in'],
        check_in_date__lt=check_out_date,
        check_out_date__gt=check_in_date
    ).values_list('room_id', flat=True)
    
    available_rooms = Room.objects.filter(
        is_active=True,
        status='available'
    ).exclude(id__in=occupied_rooms).select_related('room_type')
    
    rooms_data = []
    for room in available_rooms:
        nights = (check_out_date - check_in_date).days
        total_price = room.price_per_night * nights
        
        rooms_data.append({
            'id': room.id,
            'number': room.number,
            'type': room.room_type.name,
            'floor': room.floor,
            'price_per_night': float(room.price_per_night),
            'total_price': float(total_price),
            'max_occupancy': room.room_type.max_occupancy,
            'amenities': room.room_type.amenities,
        })
    
    return Response({
        'check_in': check_in,
        'check_out': check_out,
        'nights': (check_out_date - check_in_date).days,
        'available_rooms': rooms_data,
        'total_available': len(rooms_data),
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def booking_calendar(request):
    """API لتقويم الحجوزات"""
    start_date = request.GET.get('start')
    end_date = request.GET.get('end')
    
    if not start_date or not end_date:
        # افتراضي: الشهر الحالي
        today = date.today()
        start_date = today.replace(day=1)
        end_date = (start_date + timedelta(days=32)).replace(day=1) - timedelta(days=1)
    else:
        try:
            start_date = date.fromisoformat(start_date)
            end_date = date.fromisoformat(end_date)
        except ValueError:
            return Response({'error': 'تنسيق التاريخ غير صحيح'}, status=400)
    
    bookings = Booking.objects.filter(
        Q(check_in_date__lte=end_date) & Q(check_out_date__gte=start_date)
    ).select_related('customer', 'room').order_by('check_in_date')
    
    events = []
    for booking in bookings:
        color = {
            'pending': '#ffc107',
            'confirmed': '#28a745',
            'checked_in': '#007bff',
            'checked_out': '#6c757d',
            'cancelled': '#dc3545',
            'no_show': '#fd7e14',
        }.get(booking.status, '#6c757d')
        
        events.append({
            'id': booking.id,
            'title': f"{booking.customer.full_name} - غرفة {booking.room.number}",
            'start': booking.check_in_date.isoformat(),
            'end': booking.check_out_date.isoformat(),
            'color': color,
            'booking_number': booking.booking_number,
            'status': booking.get_status_display(),
            'customer': booking.customer.full_name,
            'room': booking.room.number,
            'adults': booking.adults,
            'children': booking.children,
            'total_amount': float(booking.total_amount),
            'paid_amount': float(booking.paid_amount),
        })
    
    return Response({
        'events': events,
        'start_date': start_date.isoformat(),
        'end_date': end_date.isoformat(),
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def quick_checkin(request):
    """API لتسجيل الوصول السريع"""
    booking_id = request.data.get('booking_id')
    
    if not booking_id:
        return Response({'error': 'يجب تحديد رقم الحجز'}, status=400)
    
    try:
        booking = Booking.objects.get(id=booking_id)
    except Booking.DoesNotExist:
        return Response({'error': 'الحجز غير موجود'}, status=404)
    
    if booking.status != 'confirmed':
        return Response({'error': 'لا يمكن تسجيل الوصول لهذا الحجز'}, status=400)
    
    # تحديث حالة الحجز والغرفة
    booking.status = 'checked_in'
    booking.actual_check_in = timezone.now()
    booking.save()
    
    booking.room.status = 'occupied'
    booking.room.save()
    
    return Response({
        'success': True,
        'message': f'تم تسجيل وصول {booking.customer.full_name} بنجاح',
        'booking': {
            'id': booking.id,
            'booking_number': booking.booking_number,
            'customer': booking.customer.full_name,
            'room': booking.room.number,
            'check_in_time': booking.actual_check_in.isoformat(),
        }
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def quick_checkout(request):
    """API لتسجيل المغادرة السريع"""
    booking_id = request.data.get('booking_id')
    
    if not booking_id:
        return Response({'error': 'يجب تحديد رقم الحجز'}, status=400)
    
    try:
        booking = Booking.objects.get(id=booking_id)
    except Booking.DoesNotExist:
        return Response({'error': 'الحجز غير موجود'}, status=404)
    
    if booking.status != 'checked_in':
        return Response({'error': 'لا يمكن تسجيل المغادرة لهذا الحجز'}, status=400)
    
    # تحديث حالة الحجز والغرفة
    booking.status = 'checked_out'
    booking.actual_check_out = timezone.now()
    booking.save()
    
    booking.room.status = 'cleaning'
    booking.room.save()
    
    return Response({
        'success': True,
        'message': f'تم تسجيل مغادرة {booking.customer.full_name} بنجاح',
        'booking': {
            'id': booking.id,
            'booking_number': booking.booking_number,
            'customer': booking.customer.full_name,
            'room': booking.room.number,
            'check_out_time': booking.actual_check_out.isoformat(),
            'remaining_amount': float(booking.remaining_amount),
        }
    })
