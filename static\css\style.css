/* نمط عام للتطبيق */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
}

/* تحسين الخط العربي */
.arabic-text {
    font-family: '<PERSON><PERSON>', 'Times New Roman', serif;
}

/* بطاقات مخصصة */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

/* أزرار مخصصة */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 10px 25px;
    transition: all 0.3s;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border: none;
    border-radius: 25px;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
    border-radius: 25px;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    border-radius: 25px;
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    border-radius: 25px;
}

/* شريط التنقل */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-card .stats-label {
    font-size: 1rem;
    opacity: 0.9;
}

.stats-card.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* جداول مخصصة */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* نماذج مخصصة */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* حالات الغرف */
.room-status {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.room-status.available {
    background-color: #d4edda;
    color: #155724;
}

.room-status.occupied {
    background-color: #f8d7da;
    color: #721c24;
}

.room-status.maintenance {
    background-color: #fff3cd;
    color: #856404;
}

.room-status.cleaning {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* حالات الحجز */
.booking-status {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.booking-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.booking-status.confirmed {
    background-color: #d4edda;
    color: #155724;
}

.booking-status.checked_in {
    background-color: #d1ecf1;
    color: #0c5460;
}

.booking-status.checked_out {
    background-color: #e2e3e5;
    color: #383d41;
}

.booking-status.cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 15px;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
    
    .table-responsive {
        border-radius: 10px;
    }
}

/* تحسينات للطباعة */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* تحسينات إضافية */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
    border-radius: 0 0 20px 20px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* تحسين الأيقونات */
.icon-lg {
    font-size: 3rem;
    margin-bottom: 15px;
}

.icon-md {
    font-size: 1.5rem;
}

/* تحسين المسافات */
.section-spacing {
    margin: 40px 0;
}

/* تحسين الظلال */
.shadow-custom {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
}

/* تحسين الحدود */
.border-custom {
    border: 2px solid #e9ecef !important;
    border-radius: 10px !important;
}

/* تحسين النصوص */
.text-muted-custom {
    color: #6c757d !important;
    font-size: 0.9rem;
}

/* تحسين الخلفيات */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}
