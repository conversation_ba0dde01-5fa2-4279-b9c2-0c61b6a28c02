from django.db import models
from django.core.validators import MinValueValidator

class RoomType(models.Model):
    """نموذج أنواع الغرف"""
    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='اسم نوع الغرفة'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )
    base_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='السعر الأساسي'
    )
    max_occupancy = models.PositiveIntegerField(
        default=2,
        verbose_name='الحد الأقصى للإشغال'
    )
    amenities = models.TextField(
        blank=True,
        null=True,
        verbose_name='المرافق'
    )

    class Meta:
        verbose_name = 'نوع غرفة'
        verbose_name_plural = 'أنواع الغرف'

    def __str__(self):
        return self.name

class Room(models.Model):
    """نموذج الغرف"""
    ROOM_STATUS = (
        ('available', 'متاحة'),
        ('occupied', 'مشغولة'),
        ('maintenance', 'صيانة'),
        ('cleaning', 'تنظيف'),
    )

    number = models.CharField(
        max_length=10,
        unique=True,
        verbose_name='رقم الغرفة'
    )
    room_type = models.ForeignKey(
        RoomType,
        on_delete=models.CASCADE,
        verbose_name='نوع الغرفة'
    )
    floor = models.PositiveIntegerField(
        verbose_name='الطابق'
    )
    status = models.CharField(
        max_length=20,
        choices=ROOM_STATUS,
        default='available',
        verbose_name='حالة الغرفة'
    )
    price_per_night = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='سعر الليلة'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشطة'
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'غرفة'
        verbose_name_plural = 'الغرف'
        ordering = ['number']

    def __str__(self):
        return f"غرفة {self.number} - {self.room_type.name}"

    @property
    def is_available(self):
        """التحقق من توفر الغرفة"""
        return self.status == 'available' and self.is_active
