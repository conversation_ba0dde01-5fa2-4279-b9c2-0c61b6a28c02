from django.contrib import admin
from .models import Customer

@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'email', 'phone', 'nationality', 'is_vip', 'created_at')
    list_filter = ('gender', 'nationality', 'is_vip', 'id_type')
    search_fields = ('first_name', 'last_name', 'email', 'phone', 'id_number')
    list_editable = ('is_vip',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('المعلومات الشخصية', {
            'fields': ('first_name', 'last_name', 'gender', 'date_of_birth', 'nationality')
        }),
        ('معلومات الاتصال', {
            'fields': ('email', 'phone', 'address')
        }),
        ('الهوية', {
            'fields': ('id_type', 'id_number')
        }),
        ('جهة الاتصال الطارئ', {
            'fields': ('emergency_contact_name', 'emergency_contact_phone')
        }),
        ('إعدادات إضافية', {
            'fields': ('is_vip', 'notes')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
