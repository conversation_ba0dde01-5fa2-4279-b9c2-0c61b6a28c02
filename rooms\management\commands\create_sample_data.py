from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from rooms.models import RoomType, Room
from customers.models import Customer
from bookings.models import Booking, Payment
from decimal import Decimal
from datetime import date, timedelta
import random

User = get_user_model()

class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للفندق'

    def handle(self, *args, **options):
        self.stdout.write('بدء إنشاء البيانات التجريبية...')
        
        # إنشاء أنواع الغرف
        room_types_data = [
            {
                'name': 'غرفة مفردة',
                'description': 'غرفة مفردة مريحة مع سرير واحد',
                'base_price': Decimal('200.00'),
                'max_occupancy': 1,
                'amenities': 'تلفزيون، واي فاي، مكيف هواء، حمام خاص'
            },
            {
                'name': 'غرفة مزدوجة',
                'description': 'غرفة مزدوجة مع سريرين أو سرير كبير',
                'base_price': Decimal('350.00'),
                'max_occupancy': 2,
                'amenities': 'تلفزيون، واي فاي، مكيف هواء، حمام خاص، ثلاجة صغيرة'
            },
            {
                'name': 'جناح عائلي',
                'description': 'جناح واسع للعائلات',
                'base_price': Decimal('500.00'),
                'max_occupancy': 4,
                'amenities': 'تلفزيون، واي فاي، مكيف هواء، حمام خاص، ثلاجة، غرفة معيشة'
            },
            {
                'name': 'جناح ملكي',
                'description': 'جناح فاخر مع إطلالة رائعة',
                'base_price': Decimal('800.00'),
                'max_occupancy': 2,
                'amenities': 'تلفزيون، واي فاي، مكيف هواء، حمام فاخر، جاكوزي، شرفة، خدمة الغرف'
            }
        ]
        
        room_types = []
        for room_type_data in room_types_data:
            room_type, created = RoomType.objects.get_or_create(
                name=room_type_data['name'],
                defaults=room_type_data
            )
            room_types.append(room_type)
            if created:
                self.stdout.write(f'تم إنشاء نوع غرفة: {room_type.name}')
        
        # إنشاء الغرف
        rooms_data = []
        for floor in range(1, 5):  # 4 طوابق
            for room_num in range(1, 11):  # 10 غرف في كل طابق
                room_number = f"{floor}{room_num:02d}"
                room_type = random.choice(room_types)
                
                # تحديد السعر بناءً على نوع الغرفة مع تنويع بسيط
                price_variation = random.uniform(0.9, 1.1)
                price = room_type.base_price * Decimal(str(price_variation))
                
                rooms_data.append({
                    'number': room_number,
                    'room_type': room_type,
                    'floor': floor,
                    'price_per_night': price.quantize(Decimal('0.01')),
                    'status': random.choice(['available', 'occupied', 'maintenance', 'cleaning']),
                    'is_active': True
                })
        
        for room_data in rooms_data:
            room, created = Room.objects.get_or_create(
                number=room_data['number'],
                defaults=room_data
            )
            if created:
                self.stdout.write(f'تم إنشاء غرفة: {room.number}')
        
        # إنشاء عملاء تجريبيين
        customers_data = [
            {
                'first_name': 'أحمد',
                'last_name': 'محمد',
                'email': '<EMAIL>',
                'phone': '+966501234567',
                'gender': 'M',
                'id_type': 'national_id',
                'id_number': '1234567890',
                'nationality': 'سعودي',
                'is_vip': True
            },
            {
                'first_name': 'فاطمة',
                'last_name': 'علي',
                'email': '<EMAIL>',
                'phone': '+966507654321',
                'gender': 'F',
                'id_type': 'national_id',
                'id_number': '0987654321',
                'nationality': 'سعودي',
                'is_vip': False
            },
            {
                'first_name': 'محمد',
                'last_name': 'السعيد',
                'email': '<EMAIL>',
                'phone': '+966512345678',
                'gender': 'M',
                'id_type': 'passport',
                'id_number': 'P123456789',
                'nationality': 'مصري',
                'is_vip': True
            },
            {
                'first_name': 'نورا',
                'last_name': 'أحمد',
                'email': '<EMAIL>',
                'phone': '+966598765432',
                'gender': 'F',
                'id_type': 'national_id',
                'id_number': '5678901234',
                'nationality': 'سعودي',
                'is_vip': False
            }
        ]
        
        customers = []
        for customer_data in customers_data:
            customer, created = Customer.objects.get_or_create(
                email=customer_data['email'],
                defaults=customer_data
            )
            customers.append(customer)
            if created:
                self.stdout.write(f'تم إنشاء عميل: {customer.full_name}')
        
        # إنشاء حجوزات تجريبية
        admin_user = User.objects.filter(is_superuser=True).first()
        available_rooms = Room.objects.filter(status='available')[:5]
        
        for i, room in enumerate(available_rooms):
            customer = customers[i % len(customers)]
            check_in = date.today() + timedelta(days=random.randint(-10, 10))
            check_out = check_in + timedelta(days=random.randint(1, 7))
            
            booking_data = {
                'customer': customer,
                'room': room,
                'check_in_date': check_in,
                'check_out_date': check_out,
                'adults': random.randint(1, room.room_type.max_occupancy),
                'children': random.randint(0, 2),
                'status': random.choice(['pending', 'confirmed', 'checked_in']),
                'created_by': admin_user,
                'special_requests': 'طلبات خاصة تجريبية'
            }
            
            booking = Booking.objects.create(**booking_data)
            self.stdout.write(f'تم إنشاء حجز: {booking.booking_number}')
            
            # إضافة دفعة للحجز
            if random.choice([True, False]):
                payment_amount = booking.total_amount * Decimal(str(random.uniform(0.3, 1.0)))
                Payment.objects.create(
                    booking=booking,
                    amount=payment_amount.quantize(Decimal('0.01')),
                    payment_method=random.choice(['cash', 'credit_card', 'bank_transfer']),
                    processed_by=admin_user
                )
                self.stdout.write(f'تم إنشاء دفعة للحجز: {booking.booking_number}')
        
        self.stdout.write(
            self.style.SUCCESS('تم إنشاء البيانات التجريبية بنجاح!')
        )
