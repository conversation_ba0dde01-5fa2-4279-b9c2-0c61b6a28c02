"""
إعدادات التطوير لنظام إدارة الفندق
"""

from .settings import *

# إعدادات التطوير
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# إعدادات قاعدة البيانات للتطوير (SQLite)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# إضافة أدوات التطوير
INSTALLED_APPS += [
    'django_extensions',
]

# إعدادات التسجيل للتطوير
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# إعدادات البريد الإلكتروني للتطوير
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# إعدادات إضافية للتطوير
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

# تعطيل بعض إعدادات الأمان للتطوير
SECURE_SSL_REDIRECT = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False
