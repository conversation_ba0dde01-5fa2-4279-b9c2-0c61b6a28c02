from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import Booking, Payment
from customers.models import Customer
from rooms.models import Room
from datetime import date, timedelta

class BookingForm(forms.ModelForm):
    """نموذج إضافة/تعديل الحجز"""
    class Meta:
        model = Booking
        fields = [
            'customer', 'room', 'check_in_date', 'check_out_date',
            'adults', 'children', 'status', 'special_requests', 'notes'
        ]
        widgets = {
            'customer': forms.Select(attrs={
                'class': 'form-control'
            }),
            'room': forms.Select(attrs={
                'class': 'form-control'
            }),
            'check_in_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'check_out_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'adults': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'value': '1'
            }),
            'children': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'value': '0'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'special_requests': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'طلبات خاصة (اختياري)'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
        }
        labels = {
            'customer': 'العميل',
            'room': 'الغرفة',
            'check_in_date': 'تاريخ الوصول',
            'check_out_date': 'تاريخ المغادرة',
            'adults': 'عدد البالغين',
            'children': 'عدد الأطفال',
            'status': 'حالة الحجز',
            'special_requests': 'طلبات خاصة',
            'notes': 'ملاحظات',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تحديد الغرف المتاحة فقط
        self.fields['room'].queryset = Room.objects.filter(is_active=True)
        # تحديد التاريخ الافتراضي
        if not self.instance.pk:
            self.fields['check_in_date'].initial = date.today()
            self.fields['check_out_date'].initial = date.today() + timedelta(days=1)

    def clean(self):
        cleaned_data = super().clean()
        check_in_date = cleaned_data.get('check_in_date')
        check_out_date = cleaned_data.get('check_out_date')
        room = cleaned_data.get('room')
        adults = cleaned_data.get('adults')
        children = cleaned_data.get('children')

        # التحقق من التواريخ
        if check_in_date and check_out_date:
            if check_in_date >= check_out_date:
                raise ValidationError('تاريخ المغادرة يجب أن يكون بعد تاريخ الوصول')
            
            if check_in_date < date.today():
                raise ValidationError('لا يمكن الحجز في تاريخ سابق')

        # التحقق من توفر الغرفة
        if room and check_in_date and check_out_date:
            conflicting_bookings = Booking.objects.filter(
                room=room,
                status__in=['confirmed', 'checked_in'],
                check_in_date__lt=check_out_date,
                check_out_date__gt=check_in_date
            )
            
            if self.instance.pk:
                conflicting_bookings = conflicting_bookings.exclude(pk=self.instance.pk)
            
            if conflicting_bookings.exists():
                raise ValidationError('الغرفة غير متاحة في هذه الفترة')

        # التحقق من عدد الأشخاص
        if room and adults and children:
            total_guests = adults + children
            if total_guests > room.room_type.max_occupancy:
                raise ValidationError(
                    f'عدد الضيوف ({total_guests}) يتجاوز الحد الأقصى للغرفة ({room.room_type.max_occupancy})'
                )

        return cleaned_data

class PaymentForm(forms.ModelForm):
    """نموذج إضافة دفعة"""
    class Meta:
        model = Payment
        fields = ['amount', 'payment_method', 'transaction_id', 'notes']
        widgets = {
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01'
            }),
            'payment_method': forms.Select(attrs={
                'class': 'form-control'
            }),
            'transaction_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم المعاملة (اختياري)'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات الدفعة'
            }),
        }
        labels = {
            'amount': 'المبلغ (ريال)',
            'payment_method': 'طريقة الدفع',
            'transaction_id': 'رقم المعاملة',
            'notes': 'ملاحظات',
        }

    def __init__(self, booking=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.booking = booking
        if booking:
            # تحديد المبلغ المتبقي كافتراضي
            remaining = booking.remaining_amount
            if remaining > 0:
                self.fields['amount'].initial = remaining

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if self.booking and amount:
            if amount > self.booking.remaining_amount:
                raise ValidationError(
                    f'المبلغ يتجاوز المبلغ المتبقي ({self.booking.remaining_amount} ريال)'
                )
        return amount

class BookingSearchForm(forms.Form):
    """نموذج البحث في الحجوزات"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث برقم الحجز أو اسم العميل...'
        }),
        label='البحث'
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Booking.BOOKING_STATUS,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='حالة الحجز'
    )
    check_in_date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='تاريخ الوصول من'
    )
    check_in_date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='تاريخ الوصول إلى'
    )
    room_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الغرفة'
        }),
        label='رقم الغرفة'
    )

class CheckInForm(forms.Form):
    """نموذج تسجيل الوصول"""
    actual_check_in = forms.DateTimeField(
        initial=timezone.now,
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        }),
        label='وقت الوصول الفعلي'
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'ملاحظات تسجيل الوصول'
        }),
        label='ملاحظات'
    )

class CheckOutForm(forms.Form):
    """نموذج تسجيل المغادرة"""
    actual_check_out = forms.DateTimeField(
        initial=timezone.now,
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        }),
        label='وقت المغادرة الفعلي'
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'ملاحظات تسجيل المغادرة'
        }),
        label='ملاحظات'
    )
