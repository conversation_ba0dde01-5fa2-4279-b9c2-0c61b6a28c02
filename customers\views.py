from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import Customer

@login_required
def customer_list(request):
    """قائمة العملاء"""
    customers = Customer.objects.all()
    return render(request, 'customers/list.html', {'customers': customers})

@login_required
def customer_add(request):
    """إضافة عميل جديد"""
    return render(request, 'customers/add.html')

@login_required
def customer_detail(request, pk):
    """تفاصيل العميل"""
    customer = get_object_or_404(Customer, pk=pk)
    return render(request, 'customers/detail.html', {'customer': customer})

@login_required
def customer_edit(request, pk):
    """تعديل عميل"""
    customer = get_object_or_404(Customer, pk=pk)
    return render(request, 'customers/edit.html', {'customer': customer})

@login_required
def customer_delete(request, pk):
    """حذف عميل"""
    customer = get_object_or_404(Customer, pk=pk)
    if request.method == 'POST':
        customer.delete()
        messages.success(request, 'تم حذف العميل بنجاح')
    return redirect('customers:list')
