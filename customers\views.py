from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count
from django.core.paginator import Paginator
from .models import Customer
from .forms import CustomerForm, CustomerSearchForm
from bookings.models import Booking

@login_required
def customer_list(request):
    """قائمة العملاء مع البحث والتصفية"""
    customers = Customer.objects.annotate(
        total_bookings=Count('booking'),
        active_bookings=Count('booking', filter=Q(booking__status__in=['confirmed', 'checked_in']))
    ).order_by('-created_at')

    search_form = CustomerSearchForm(request.GET)

    if search_form.is_valid():
        search = search_form.cleaned_data.get('search')
        nationality = search_form.cleaned_data.get('nationality')
        gender = search_form.cleaned_data.get('gender')
        is_vip = search_form.cleaned_data.get('is_vip')
        id_type = search_form.cleaned_data.get('id_type')

        if search:
            customers = customers.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search) |
                Q(phone__icontains=search) |
                Q(id_number__icontains=search)
            )

        if nationality:
            customers = customers.filter(nationality__icontains=nationality)

        if gender:
            customers = customers.filter(gender=gender)

        if is_vip:
            customers = customers.filter(is_vip=(is_vip == 'true'))

        if id_type:
            customers = customers.filter(id_type=id_type)

    # ترقيم الصفحات
    paginator = Paginator(customers, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات سريعة
    stats = {
        'total': Customer.objects.count(),
        'vip': Customer.objects.filter(is_vip=True).count(),
        'regular': Customer.objects.filter(is_vip=False).count(),
        'with_bookings': Customer.objects.filter(booking__isnull=False).distinct().count(),
    }

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'stats': stats,
    }
    return render(request, 'customers/list.html', context)

@login_required
def customer_add(request):
    """إضافة عميل جديد"""
    if request.method == 'POST':
        form = CustomerForm(request.POST)
        if form.is_valid():
            customer = form.save()
            messages.success(request, f'تم إضافة العميل {customer.full_name} بنجاح')
            return redirect('customers:detail', pk=customer.pk)
    else:
        form = CustomerForm()

    return render(request, 'customers/add.html', {'form': form})

@login_required
def customer_detail(request, pk):
    """تفاصيل العميل"""
    customer = get_object_or_404(Customer, pk=pk)

    # جلب حجوزات العميل
    bookings = Booking.objects.filter(customer=customer).select_related('room').order_by('-created_at')

    # إحصائيات العميل
    customer_stats = {
        'total_bookings': bookings.count(),
        'confirmed_bookings': bookings.filter(status='confirmed').count(),
        'completed_bookings': bookings.filter(status='checked_out').count(),
        'cancelled_bookings': bookings.filter(status='cancelled').count(),
        'total_spent': sum([booking.paid_amount for booking in bookings]),
    }

    context = {
        'customer': customer,
        'bookings': bookings[:10],  # آخر 10 حجوزات
        'customer_stats': customer_stats,
    }
    return render(request, 'customers/detail.html', context)

@login_required
def customer_edit(request, pk):
    """تعديل عميل"""
    customer = get_object_or_404(Customer, pk=pk)

    if request.method == 'POST':
        form = CustomerForm(request.POST, instance=customer)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات العميل {customer.full_name} بنجاح')
            return redirect('customers:detail', pk=customer.pk)
    else:
        form = CustomerForm(instance=customer)

    return render(request, 'customers/edit.html', {'form': form, 'customer': customer})

@login_required
def customer_delete(request, pk):
    """حذف عميل"""
    customer = get_object_or_404(Customer, pk=pk)

    if request.method == 'POST':
        # التحقق من وجود حجوزات نشطة
        active_bookings = customer.booking_set.filter(
            status__in=['confirmed', 'checked_in']
        ).count()

        if active_bookings > 0:
            messages.error(request, 'لا يمكن حذف العميل لوجود حجوزات نشطة')
            return redirect('customers:detail', pk=customer.pk)
        else:
            customer_name = customer.full_name
            customer.delete()
            messages.success(request, f'تم حذف العميل {customer_name} بنجاح')
            return redirect('customers:list')

    return render(request, 'customers/delete_confirm.html', {'customer': customer})
