from django.db import models
from django.core.validators import MinValueValidator
from django.contrib.auth import get_user_model
from customers.models import Customer
from rooms.models import Room
from decimal import Decimal

User = get_user_model()

class Booking(models.Model):
    """نموذج الحجوزات"""
    BOOKING_STATUS = (
        ('pending', 'في الانتظار'),
        ('confirmed', 'مؤكد'),
        ('checked_in', 'تم تسجيل الوصول'),
        ('checked_out', 'تم تسجيل المغادرة'),
        ('cancelled', 'ملغي'),
        ('no_show', 'لم يحضر'),
    )

    booking_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='رقم الحجز'
    )
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        verbose_name='العميل'
    )
    room = models.ForeignKey(
        Room,
        on_delete=models.CASCADE,
        verbose_name='الغرفة'
    )
    check_in_date = models.DateField(
        verbose_name='تاريخ الوصول'
    )
    check_out_date = models.DateField(
        verbose_name='تاريخ المغادرة'
    )
    actual_check_in = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='وقت الوصول الفعلي'
    )
    actual_check_out = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='وقت المغادرة الفعلي'
    )
    adults = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        verbose_name='عدد البالغين'
    )
    children = models.PositiveIntegerField(
        default=0,
        verbose_name='عدد الأطفال'
    )
    status = models.CharField(
        max_length=20,
        choices=BOOKING_STATUS,
        default='pending',
        verbose_name='حالة الحجز'
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='المبلغ الإجمالي'
    )
    paid_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='المبلغ المدفوع'
    )
    special_requests = models.TextField(
        blank=True,
        null=True,
        verbose_name='طلبات خاصة'
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='أنشئ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'حجز'
        verbose_name_plural = 'الحجوزات'
        ordering = ['-created_at']

    def __str__(self):
        return f"حجز {self.booking_number} - {self.customer.full_name}"

    @property
    def nights(self):
        """حساب عدد الليالي"""
        return (self.check_out_date - self.check_in_date).days

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return self.total_amount - self.paid_amount

    @property
    def is_paid(self):
        """التحقق من سداد المبلغ كاملاً"""
        return self.paid_amount >= self.total_amount

    def save(self, *args, **kwargs):
        if not self.booking_number:
            # إنشاء رقم حجز تلقائي
            import datetime
            today = datetime.date.today()
            count = Booking.objects.filter(created_at__date=today).count() + 1
            self.booking_number = f"BK{today.strftime('%Y%m%d')}{count:04d}"

        # حساب المبلغ الإجمالي
        if self.nights > 0:
            self.total_amount = self.room.price_per_night * self.nights

        super().save(*args, **kwargs)


class Payment(models.Model):
    """نموذج المدفوعات"""
    PAYMENT_METHODS = (
        ('cash', 'نقدي'),
        ('credit_card', 'بطاقة ائتمان'),
        ('debit_card', 'بطاقة خصم'),
        ('bank_transfer', 'تحويل بنكي'),
        ('online', 'دفع إلكتروني'),
    )

    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name='الحجز'
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='المبلغ'
    )
    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHODS,
        verbose_name='طريقة الدفع'
    )
    transaction_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='رقم المعاملة'
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='تم بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الدفع'
    )

    class Meta:
        verbose_name = 'دفعة'
        verbose_name_plural = 'المدفوعات'
        ordering = ['-created_at']

    def __str__(self):
        return f"دفعة {self.amount} - {self.booking.booking_number}"
