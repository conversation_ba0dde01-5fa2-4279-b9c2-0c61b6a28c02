from django.urls import path
from . import views

app_name = 'bookings'

urlpatterns = [
    path('', views.booking_list, name='list'),
    path('add/', views.booking_add, name='add'),
    path('<int:pk>/', views.booking_detail, name='detail'),
    path('<int:pk>/edit/', views.booking_edit, name='edit'),
    path('<int:pk>/delete/', views.booking_delete, name='delete'),
    path('<int:pk>/checkin/', views.booking_checkin, name='checkin'),
    path('<int:pk>/checkout/', views.booking_checkout, name='checkout'),
    path('<int:pk>/cancel/', views.booking_cancel, name='cancel'),
    path('<int:pk>/payment/', views.add_payment, name='add_payment'),
    path('calendar/', views.booking_calendar, name='calendar'),
]
