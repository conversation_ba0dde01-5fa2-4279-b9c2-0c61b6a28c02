from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Count
from django.core.paginator import Paginator
from .models import Room, RoomType
from .forms import <PERSON><PERSON>orm, RoomTypeForm, RoomSearchForm

@login_required
def room_list(request):
    """قائمة الغرف مع البحث والتصفية"""
    rooms = Room.objects.select_related('room_type').all()
    search_form = RoomSearchForm(request.GET)

    if search_form.is_valid():
        search = search_form.cleaned_data.get('search')
        room_type = search_form.cleaned_data.get('room_type')
        status = search_form.cleaned_data.get('status')
        floor = search_form.cleaned_data.get('floor')
        is_active = search_form.cleaned_data.get('is_active')

        if search:
            rooms = rooms.filter(
                Q(number__icontains=search) |
                Q(room_type__name__icontains=search)
            )

        if room_type:
            rooms = rooms.filter(room_type=room_type)

        if status:
            rooms = rooms.filter(status=status)

        if floor:
            rooms = rooms.filter(floor=floor)

        if is_active:
            rooms = rooms.filter(is_active=(is_active == 'true'))

    # ترقيم الصفحات
    paginator = Paginator(rooms, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات سريعة
    stats = {
        'total': rooms.count(),
        'available': rooms.filter(status='available').count(),
        'occupied': rooms.filter(status='occupied').count(),
        'maintenance': rooms.filter(status='maintenance').count(),
        'cleaning': rooms.filter(status='cleaning').count(),
    }

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'stats': stats,
    }
    return render(request, 'rooms/list.html', context)

@login_required
def room_add(request):
    """إضافة غرفة جديدة"""
    if request.method == 'POST':
        form = RoomForm(request.POST)
        if form.is_valid():
            room = form.save()
            messages.success(request, f'تم إضافة الغرفة {room.number} بنجاح')
            return redirect('rooms:list')
    else:
        form = RoomForm()

    return render(request, 'rooms/add.html', {'form': form})

@login_required
def room_edit(request, pk):
    """تعديل غرفة"""
    room = get_object_or_404(Room, pk=pk)

    if request.method == 'POST':
        form = RoomForm(request.POST, instance=room)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث الغرفة {room.number} بنجاح')
            return redirect('rooms:list')
    else:
        form = RoomForm(instance=room)

    return render(request, 'rooms/edit.html', {'form': form, 'room': room})

@login_required
def room_delete(request, pk):
    """حذف غرفة"""
    room = get_object_or_404(Room, pk=pk)

    if request.method == 'POST':
        # التحقق من وجود حجوزات نشطة
        active_bookings = room.booking_set.filter(
            status__in=['confirmed', 'checked_in']
        ).count()

        if active_bookings > 0:
            messages.error(request, 'لا يمكن حذف الغرفة لوجود حجوزات نشطة')
        else:
            room_number = room.number
            room.delete()
            messages.success(request, f'تم حذف الغرفة {room_number} بنجاح')

        return redirect('rooms:list')

    return render(request, 'rooms/delete_confirm.html', {'room': room})

@login_required
def room_type_list(request):
    """قائمة أنواع الغرف"""
    room_types = RoomType.objects.annotate(
        room_count=Count('room'),
        available_count=Count('room', filter=Q(room__status='available')),
        occupied_count=Count('room', filter=Q(room__status='occupied'))
    )

    context = {
        'room_types': room_types,
    }
    return render(request, 'rooms/types.html', context)

@login_required
def room_type_add(request):
    """إضافة نوع غرفة جديد"""
    if request.method == 'POST':
        form = RoomTypeForm(request.POST)
        if form.is_valid():
            room_type = form.save()
            messages.success(request, f'تم إضافة نوع الغرفة {room_type.name} بنجاح')
            return redirect('rooms:types')
    else:
        form = RoomTypeForm()

    return render(request, 'rooms/type_add.html', {'form': form})

@login_required
def room_type_edit(request, pk):
    """تعديل نوع غرفة"""
    room_type = get_object_or_404(RoomType, pk=pk)

    if request.method == 'POST':
        form = RoomTypeForm(request.POST, instance=room_type)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث نوع الغرفة {room_type.name} بنجاح')
            return redirect('rooms:types')
    else:
        form = RoomTypeForm(instance=room_type)

    return render(request, 'rooms/type_edit.html', {'form': form, 'room_type': room_type})

@login_required
def update_room_status(request):
    """تحديث حالة الغرفة عبر AJAX"""
    if request.method == 'POST':
        room_id = request.POST.get('room_id')
        status = request.POST.get('status')
        try:
            room = Room.objects.get(id=room_id)
            old_status = room.get_status_display()
            room.status = status
            room.save()

            return JsonResponse({
                'success': True,
                'message': f'تم تغيير حالة الغرفة {room.number} من {old_status} إلى {room.get_status_display()}'
            })
        except Room.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'الغرفة غير موجودة'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': 'حدث خطأ في تحديث حالة الغرفة'
            })

    return JsonResponse({'success': False, 'message': 'طلب غير صحيح'})
