from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from .models import Room, RoomType

@login_required
def room_list(request):
    """قائمة الغرف"""
    rooms = Room.objects.select_related('room_type').all()
    return render(request, 'rooms/list.html', {'rooms': rooms})

@login_required
def room_add(request):
    """إضافة غرفة جديدة"""
    return render(request, 'rooms/add.html')

@login_required
def room_edit(request, pk):
    """تعديل غرفة"""
    room = get_object_or_404(Room, pk=pk)
    return render(request, 'rooms/edit.html', {'room': room})

@login_required
def room_delete(request, pk):
    """حذف غرفة"""
    room = get_object_or_404(Room, pk=pk)
    if request.method == 'POST':
        room.delete()
        messages.success(request, 'تم حذف الغرفة بنجاح')
    return redirect('rooms:list')

@login_required
def room_type_list(request):
    """قائمة أنواع الغرف"""
    room_types = RoomType.objects.all()
    return render(request, 'rooms/types.html', {'room_types': room_types})

@login_required
def room_type_add(request):
    """إضافة نوع غرفة جديد"""
    return render(request, 'rooms/type_add.html')

@login_required
def room_type_edit(request, pk):
    """تعديل نوع غرفة"""
    room_type = get_object_or_404(RoomType, pk=pk)
    return render(request, 'rooms/type_edit.html', {'room_type': room_type})

@login_required
def update_room_status(request):
    """تحديث حالة الغرفة عبر AJAX"""
    if request.method == 'POST':
        room_id = request.POST.get('room_id')
        status = request.POST.get('status')
        try:
            room = Room.objects.get(id=room_id)
            room.status = status
            room.save()
            return JsonResponse({'success': True})
        except Room.DoesNotExist:
            return JsonResponse({'success': False})
    return JsonResponse({'success': False})
