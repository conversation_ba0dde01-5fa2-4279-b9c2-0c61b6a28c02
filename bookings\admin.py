from django.contrib import admin
from .models import Booking, Payment

class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0
    readonly_fields = ('created_at',)

@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    list_display = ('booking_number', 'customer', 'room', 'check_in_date', 'check_out_date', 'status', 'total_amount', 'is_paid')
    list_filter = ('status', 'check_in_date', 'room__room_type', 'created_at')
    search_fields = ('booking_number', 'customer__first_name', 'customer__last_name', 'room__number')
    readonly_fields = ('booking_number', 'nights', 'remaining_amount', 'created_at', 'updated_at')
    inlines = [PaymentInline]

    fieldsets = (
        ('معلومات الحجز', {
            'fields': ('booking_number', 'customer', 'room', 'status')
        }),
        ('التواريخ', {
            'fields': ('check_in_date', 'check_out_date', 'actual_check_in', 'actual_check_out')
        }),
        ('تفاصيل الإقامة', {
            'fields': ('adults', 'children', 'special_requests', 'notes')
        }),
        ('المالية', {
            'fields': ('total_amount', 'paid_amount', 'remaining_amount', 'nights')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('booking', 'amount', 'payment_method', 'processed_by', 'created_at')
    list_filter = ('payment_method', 'created_at')
    search_fields = ('booking__booking_number', 'transaction_id')
    readonly_fields = ('created_at',)
