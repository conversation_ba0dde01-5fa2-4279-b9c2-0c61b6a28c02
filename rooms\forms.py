from django import forms
from .models import Room, RoomType

class RoomTypeForm(forms.ModelForm):
    """نموذج إضافة/تعديل نوع الغرفة"""
    class Meta:
        model = RoomType
        fields = ['name', 'description', 'base_price', 'max_occupancy', 'amenities']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم نوع الغرفة'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف نوع الغرفة'
            }),
            'base_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'max_occupancy': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'placeholder': '2'
            }),
            'amenities': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'المرافق المتاحة (مفصولة بفواصل)'
            }),
        }
        labels = {
            'name': 'اسم نوع الغرفة',
            'description': 'الوصف',
            'base_price': 'السعر الأساسي (ريال)',
            'max_occupancy': 'الحد الأقصى للإشغال',
            'amenities': 'المرافق',
        }

class RoomForm(forms.ModelForm):
    """نموذج إضافة/تعديل الغرفة"""
    class Meta:
        model = Room
        fields = ['number', 'room_type', 'floor', 'status', 'price_per_night', 'is_active', 'notes']
        widgets = {
            'number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الغرفة (مثال: 101)'
            }),
            'room_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'floor': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'placeholder': '1'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'price_per_night': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
        }
        labels = {
            'number': 'رقم الغرفة',
            'room_type': 'نوع الغرفة',
            'floor': 'الطابق',
            'status': 'حالة الغرفة',
            'price_per_night': 'سعر الليلة (ريال)',
            'is_active': 'نشطة',
            'notes': 'ملاحظات',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تحديد السعر الافتراضي بناءً على نوع الغرفة
        if 'room_type' in self.data:
            try:
                room_type_id = int(self.data.get('room_type'))
                room_type = RoomType.objects.get(id=room_type_id)
                if not self.data.get('price_per_night'):
                    self.fields['price_per_night'].initial = room_type.base_price
            except (ValueError, TypeError, RoomType.DoesNotExist):
                pass

class RoomSearchForm(forms.Form):
    """نموذج البحث في الغرف"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث برقم الغرفة أو نوع الغرفة...'
        }),
        label='البحث'
    )
    room_type = forms.ModelChoiceField(
        queryset=RoomType.objects.all(),
        required=False,
        empty_label='جميع الأنواع',
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='نوع الغرفة'
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Room.ROOM_STATUS,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='حالة الغرفة'
    )
    floor = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الطابق'
        }),
        label='الطابق'
    )
    is_active = forms.ChoiceField(
        choices=[('', 'الكل'), ('true', 'نشطة'), ('false', 'غير نشطة')],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='الحالة'
    )
