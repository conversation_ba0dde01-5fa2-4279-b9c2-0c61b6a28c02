# نظام إدارة الفندق

نظام شامل لإدارة الفنادق مطور باستخدام Django مع واجهة مستخدم عربية جميلة ومتجاوبة.

## المميزات

### 🔐 إدارة المستخدمين
- تسجيل الدخول والخروج الآمن
- أنواع مستخدمين مختلفة (مدير، موظف استقبال، مدير عام)
- إدارة الملفات الشخصية

### 🏨 إدارة الغرف
- إضافة وتعديل وحذف الغرف
- أنواع غرف متعددة (مفردة، مزدوجة، جناح عائلي، جناح ملكي)
- تحديد الأسعار والتوافر
- تتبع حالة الغرف (متاحة، مشغولة، صيانة، تنظيف)

### 👥 إدارة العملاء
- إضافة بيانات العملاء الكاملة
- تصنيف العملاء (عادي، مميز)
- سجل الحجوزات لكل عميل
- معلومات الاتصال الطارئ

### 📅 إدارة الحجوزات
- إنشاء حجوزات جديدة مع التحقق من التوافر
- تعديل وإلغاء الحجوزات
- تسجيل الوصول والمغادرة
- إدارة المدفوعات المتعددة
- حالات حجز مختلفة (في الانتظار، مؤكد، تم الوصول، تم المغادرة، ملغي)

### 📊 لوحة التحكم والتقارير
- لوحة تحكم شاملة مع الإحصائيات المباشرة
- تقارير مالية مفصلة
- تقارير الإشغال ونسب الاستخدام
- تقارير العملاء

### 🔒 الأمان
- حماية من هجمات CSRF
- حماية من هجمات XSS
- حماية من SQL Injection
- تشفير كلمات المرور
- إدارة الجلسات الآمنة

### 🎨 الواجهة
- تصميم متجاوب يعمل على جميع الأجهزة
- واجهة عربية جميلة باستخدام Bootstrap
- تجربة مستخدم محسنة
- ألوان وتدرجات جذابة

## متطلبات النظام

- Python 3.8+
- Django 5.2+
- SQLite (افتراضي) أو PostgreSQL
- متصفح ويب حديث

## التثبيت والتشغيل

### الطريقة السريعة (Windows)
1. تشغيل ملف الإعداد:
```cmd
setup.bat
```

2. تشغيل التطبيق:
```cmd
run_dev.bat
```

### الطريقة اليدوية

#### 1. تحميل المشروع
```bash
git clone <repository-url>
cd HOTAL
```

#### 2. إنشاء بيئة افتراضية (مستحسن)
```bash
python -m venv hotel_env
# Windows
hotel_env\Scripts\activate
# Linux/Mac
source hotel_env/bin/activate
```

#### 3. تثبيت المتطلبات الأساسية
```bash
pip install django djangorestframework pillow python-decouple
```

#### 4. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

#### 5. إنشاء مستخدم مدير
```bash
python manage.py createsuperuser
```

#### 6. إنشاء بيانات تجريبية (اختياري)
```bash
python manage.py create_sample_data
```

#### 7. تشغيل الخادم
```bash
python manage.py runserver
```

#### 8. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://127.0.0.1:8000`

### التشغيل باستخدام Docker

#### 1. تشغيل مع Docker Compose
```bash
docker-compose up --build
```

#### 2. إعداد قاعدة البيانات (في حاوية منفصلة)
```bash
docker-compose exec web python manage.py migrate
docker-compose exec web python manage.py createsuperuser
docker-compose exec web python manage.py create_sample_data
```

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل المشروع

```
HOTAL/
├── hotel_management/          # إعدادات المشروع الرئيسية
├── accounts/                  # إدارة المستخدمين والمصادقة
├── rooms/                     # إدارة الغرف وأنواعها
├── customers/                 # إدارة العملاء
├── bookings/                  # إدارة الحجوزات والمدفوعات
├── reports/                   # التقارير والإحصائيات
├── dashboard/                 # لوحة التحكم الرئيسية
├── templates/                 # قوالب HTML
├── static/                    # ملفات CSS وJS والصور
└── manage.py                  # ملف إدارة Django
```

## النماذج الرئيسية

### User (المستخدم)
- نموذج مستخدم مخصص مع أنواع مختلفة
- معلومات إضافية مثل رقم الهاتف ونوع المستخدم

### RoomType (نوع الغرفة)
- اسم النوع والوصف
- السعر الأساسي والحد الأقصى للإشغال
- المرافق المتاحة

### Room (الغرفة)
- رقم الغرفة والطابق
- نوع الغرفة والحالة
- السعر والملاحظات

### Customer (العميل)
- المعلومات الشخصية الكاملة
- معلومات الهوية والاتصال
- تصنيف العميل (عادي/مميز)

### Booking (الحجز)
- تفاصيل الحجز والتواريخ
- العميل والغرفة المحجوزة
- حالة الحجز والمبالغ

### Payment (الدفعة)
- مبلغ الدفعة وطريقة الدفع
- رقم المعاملة والملاحظات
- المستخدم الذي عالج الدفعة

## الميزات المتقدمة

### API RESTful
- نقاط نهاية API للتكامل مع التطبيقات الخارجية
- إحصائيات لوحة التحكم المباشرة
- البحث عن الغرف المتاحة
- تقويم الحجوزات
- تسجيل الوصول والمغادرة السريع
- توثيق تلقائي للـ API
- دعم التوثيق والترخيص

#### نقاط النهاية الرئيسية:
- `GET /dashboard/api/stats/` - إحصائيات عامة
- `GET /dashboard/api/room-availability/` - توفر الغرف
- `GET /dashboard/api/booking-calendar/` - تقويم الحجوزات
- `POST /dashboard/api/quick-checkin/` - تسجيل وصول سريع
- `POST /dashboard/api/quick-checkout/` - تسجيل مغادرة سريع

### التقارير
- تقارير مالية شاملة
- إحصائيات الإشغال
- تحليل أداء العملاء
- تصدير التقارير

### الأمان
- حماية شاملة من الهجمات الشائعة
- إدارة الصلاحيات والأذونات
- تسجيل العمليات والأنشطة

## التطوير المستقبلي

- [ ] تطبيق جوال
- [ ] نظام إشعارات متقدم
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] نظام إدارة المخزون
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] نظام حجز أونلاين للعملاء
- [ ] تكامل مع أنظمة إدارة الفنادق الأخرى

## الدعم والمساهمة

لأي استفسارات أو مشاكل، يرجى فتح issue في المستودع أو التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**تم تطوير هذا النظام باستخدام أفضل الممارسات في تطوير تطبيقات الويب مع التركيز على الأمان وتجربة المستخدم.**
