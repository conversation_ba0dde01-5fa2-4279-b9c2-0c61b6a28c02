# Generated by Django 5.2.2 on 2025-06-09 23:09

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=50, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(max_length=50, verbose_name='اسم العائلة')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+*********'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('gender', models.CharField(choices=[('M', 'ذكر'), ('F', 'أنثى')], max_length=1, verbose_name='الجنس')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('id_type', models.CharField(choices=[('national_id', 'هوية وطنية'), ('passport', 'جواز سفر'), ('driving_license', 'رخصة قيادة')], default='national_id', max_length=20, verbose_name='نوع الهوية')),
                ('id_number', models.CharField(max_length=50, verbose_name='رقم الهوية')),
                ('nationality', models.CharField(default='سعودي', max_length=50, verbose_name='الجنسية')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('emergency_contact_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم جهة الاتصال الطارئ')),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=17, null=True, verbose_name='هاتف جهة الاتصال الطارئ')),
                ('is_vip', models.BooleanField(default=False, verbose_name='عميل مميز')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
                'unique_together': {('id_type', 'id_number')},
            },
        ),
    ]
