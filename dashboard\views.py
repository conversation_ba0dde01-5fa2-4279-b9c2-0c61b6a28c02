from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta
from rooms.models import Room, RoomType
from customers.models import Customer
from bookings.models import Booking, Payment

@login_required
def dashboard_home(request):
    """لوحة التحكم الرئيسية"""
    today = timezone.now().date()

    # إحصائيات الغرف
    total_rooms = Room.objects.filter(is_active=True).count()
    available_rooms = Room.objects.filter(status='available', is_active=True).count()
    occupied_rooms = Room.objects.filter(status='occupied', is_active=True).count()
    maintenance_rooms = Room.objects.filter(status='maintenance', is_active=True).count()

    # إحصائيات الحجوزات
    total_bookings = Booking.objects.count()
    today_checkins = Booking.objects.filter(
        check_in_date=today,
        status__in=['confirmed', 'checked_in']
    ).count()
    today_checkouts = Booking.objects.filter(
        check_out_date=today,
        status='checked_in'
    ).count()
    pending_bookings = Booking.objects.filter(status='pending').count()

    # إحصائيات العملاء
    total_customers = Customer.objects.count()
    vip_customers = Customer.objects.filter(is_vip=True).count()

    # الإيرادات
    this_month = timezone.now().replace(day=1)
    monthly_revenue = Payment.objects.filter(
        created_at__gte=this_month
    ).aggregate(total=Sum('amount'))['total'] or 0

    today_revenue = Payment.objects.filter(
        created_at__date=today
    ).aggregate(total=Sum('amount'))['total'] or 0

    # الحجوزات الأخيرة
    recent_bookings = Booking.objects.select_related('customer', 'room').order_by('-created_at')[:5]

    # الوصول اليوم
    today_arrivals = Booking.objects.filter(
        check_in_date=today,
        status__in=['confirmed', 'checked_in']
    ).select_related('customer', 'room')

    # المغادرة اليوم
    today_departures = Booking.objects.filter(
        check_out_date=today,
        status='checked_in'
    ).select_related('customer', 'room')

    # إحصائيات أنواع الغرف
    room_types_stats = RoomType.objects.annotate(
        total_rooms=Count('room'),
        available_rooms=Count('room', filter=Q(room__status='available')),
        occupied_rooms=Count('room', filter=Q(room__status='occupied'))
    )

    context = {
        'total_rooms': total_rooms,
        'available_rooms': available_rooms,
        'occupied_rooms': occupied_rooms,
        'maintenance_rooms': maintenance_rooms,
        'total_bookings': total_bookings,
        'today_checkins': today_checkins,
        'today_checkouts': today_checkouts,
        'pending_bookings': pending_bookings,
        'total_customers': total_customers,
        'vip_customers': vip_customers,
        'monthly_revenue': monthly_revenue,
        'today_revenue': today_revenue,
        'recent_bookings': recent_bookings,
        'today_arrivals': today_arrivals,
        'today_departures': today_departures,
        'room_types_stats': room_types_stats,
        'occupancy_rate': round((occupied_rooms / total_rooms * 100) if total_rooms > 0 else 0, 1),
    }

    return render(request, 'dashboard/home.html', context)
