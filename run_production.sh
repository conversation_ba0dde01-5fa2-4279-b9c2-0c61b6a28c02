#!/bin/bash

echo "========================================="
echo "       نظام إدارة الفندق - الإنتاج"
echo "========================================="
echo

# تعيين متغيرات البيئة
export DJANGO_SETTINGS_MODULE=hotel_management.settings_production

echo "تجميع الملفات الثابتة..."
python manage.py collectstatic --noinput

echo "تطبيق الهجرات..."
python manage.py migrate

echo "تشغيل الخادم في وضع الإنتاج..."
echo "الخادم متاح على المنفذ 8000"
echo

# تشغيل gunicorn
gunicorn --bind 0.0.0.0:8000 \
         --workers 3 \
         --timeout 120 \
         --keep-alive 2 \
         --max-requests 1000 \
         --max-requests-jitter 100 \
         --access-logfile - \
         --error-logfile - \
         hotel_management.wsgi:application
