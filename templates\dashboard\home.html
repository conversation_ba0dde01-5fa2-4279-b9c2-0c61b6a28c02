{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التحكم - نظام إدارة الفندق{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">لوحة التحكم</h1>
                    <p class="text-muted">مرحباً بك {{ user.get_full_name|default:user.username }}</p>
                </div>
                <div>
                    <span id="current-datetime" class="text-muted"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ total_rooms }}</div>
                        <div class="stats-label">إجمالي الغرف</div>
                    </div>
                    <div class="icon-lg">
                        <i class="fas fa-bed"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card success">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ available_rooms }}</div>
                        <div class="stats-label">غرف متاحة</div>
                    </div>
                    <div class="icon-lg">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card danger">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ occupied_rooms }}</div>
                        <div class="stats-label">غرف مشغولة</div>
                    </div>
                    <div class="icon-lg">
                        <i class="fas fa-user-friends"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card info">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ occupancy_rate }}%</div>
                        <div class="stats-label">نسبة الإشغال</div>
                    </div>
                    <div class="icon-lg">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الحجوزات والإيرادات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card warning">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ today_checkins }}</div>
                        <div class="stats-label">وصول اليوم</div>
                    </div>
                    <div class="icon-lg">
                        <i class="fas fa-sign-in-alt"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card info">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ today_checkouts }}</div>
                        <div class="stats-label">مغادرة اليوم</div>
                    </div>
                    <div class="icon-lg">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card success">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ today_revenue|floatformat:0 }}</div>
                        <div class="stats-label">إيرادات اليوم (ريال)</div>
                    </div>
                    <div class="icon-lg">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ monthly_revenue|floatformat:0 }}</div>
                        <div class="stats-label">إيرادات الشهر (ريال)</div>
                    </div>
                    <div class="icon-lg">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الجداول والمعلومات التفصيلية -->
    <div class="row">
        <!-- الوصول اليوم -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        الوصول اليوم
                    </h5>
                </div>
                <div class="card-body">
                    {% if today_arrivals %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>الغرفة</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in today_arrivals %}
                                    <tr>
                                        <td>{{ booking.customer.full_name }}</td>
                                        <td>{{ booking.room.number }}</td>
                                        <td>
                                            <span class="booking-status {{ booking.status }}">
                                                {{ booking.get_status_display }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">لا توجد حجوزات وصول اليوم</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- المغادرة اليوم -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-times me-2"></i>
                        المغادرة اليوم
                    </h5>
                </div>
                <div class="card-body">
                    {% if today_departures %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>الغرفة</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in today_departures %}
                                    <tr>
                                        <td>{{ booking.customer.full_name }}</td>
                                        <td>{{ booking.room.number }}</td>
                                        <td>
                                            <span class="booking-status {{ booking.status }}">
                                                {{ booking.get_status_display }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">لا توجد حجوزات مغادرة اليوم</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- الحجوزات الأخيرة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        الحجوزات الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_bookings %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>رقم الحجز</th>
                                        <th>العميل</th>
                                        <th>الغرفة</th>
                                        <th>تاريخ الوصول</th>
                                        <th>تاريخ المغادرة</th>
                                        <th>الحالة</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in recent_bookings %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'bookings:detail' booking.pk %}" class="text-decoration-none">
                                                {{ booking.booking_number }}
                                            </a>
                                        </td>
                                        <td>{{ booking.customer.full_name }}</td>
                                        <td>{{ booking.room.number }}</td>
                                        <td>{{ booking.check_in_date }}</td>
                                        <td>{{ booking.check_out_date }}</td>
                                        <td>
                                            <span class="booking-status {{ booking.status }}">
                                                {{ booking.get_status_display }}
                                            </span>
                                        </td>
                                        <td>{{ booking.total_amount|floatformat:2 }} ريال</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center">
                            <a href="{% url 'bookings:list' %}" class="btn btn-primary">
                                عرض جميع الحجوزات
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">لا توجد حجوزات</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(function() {
        location.reload();
    }, 30000);
</script>
{% endblock %}
