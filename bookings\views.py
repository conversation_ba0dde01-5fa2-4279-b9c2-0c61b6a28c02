from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import Booking, Payment

@login_required
def booking_list(request):
    """قائمة الحجوزات"""
    bookings = Booking.objects.select_related('customer', 'room').all()
    return render(request, 'bookings/list.html', {'bookings': bookings})

@login_required
def booking_add(request):
    """إضافة حجز جديد"""
    return render(request, 'bookings/add.html')

@login_required
def booking_detail(request, pk):
    """تفاصيل الحجز"""
    booking = get_object_or_404(Booking, pk=pk)
    return render(request, 'bookings/detail.html', {'booking': booking})

@login_required
def booking_edit(request, pk):
    """تعديل حجز"""
    booking = get_object_or_404(Booking, pk=pk)
    return render(request, 'bookings/edit.html', {'booking': booking})

@login_required
def booking_delete(request, pk):
    """حذف حجز"""
    booking = get_object_or_404(Booking, pk=pk)
    if request.method == 'POST':
        booking.delete()
        messages.success(request, 'تم حذف الحجز بنجاح')
    return redirect('bookings:list')

@login_required
def booking_checkin(request, pk):
    """تسجيل الوصول"""
    booking = get_object_or_404(Booking, pk=pk)
    return render(request, 'bookings/checkin.html', {'booking': booking})

@login_required
def booking_checkout(request, pk):
    """تسجيل المغادرة"""
    booking = get_object_or_404(Booking, pk=pk)
    return render(request, 'bookings/checkout.html', {'booking': booking})

@login_required
def booking_cancel(request, pk):
    """إلغاء الحجز"""
    booking = get_object_or_404(Booking, pk=pk)
    if request.method == 'POST':
        booking.status = 'cancelled'
        booking.save()
        messages.success(request, 'تم إلغاء الحجز بنجاح')
    return redirect('bookings:detail', pk=pk)

@login_required
def add_payment(request, pk):
    """إضافة دفعة"""
    booking = get_object_or_404(Booking, pk=pk)
    return render(request, 'bookings/add_payment.html', {'booking': booking})

@login_required
def booking_calendar(request):
    """تقويم الحجوزات"""
    return render(request, 'bookings/calendar.html')
