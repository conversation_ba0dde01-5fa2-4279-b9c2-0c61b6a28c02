{% extends 'base.html' %}

{% block title %}قائمة الغرف - نظام إدارة الفندق{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>قائمة الغرف</h2>
        <a href="{% url 'rooms:add' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة غرفة جديدة
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>رقم الغرفة</th>
                            <th>نوع الغرفة</th>
                            <th>الطابق</th>
                            <th>السعر</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for room in rooms %}
                        <tr>
                            <td>{{ room.number }}</td>
                            <td>{{ room.room_type.name }}</td>
                            <td>{{ room.floor }}</td>
                            <td>{{ room.price_per_night }} ريال</td>
                            <td>
                                <span class="room-status {{ room.status }}">
                                    {{ room.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <a href="{% url 'rooms:edit' room.pk %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'rooms:delete' room.pk %}" class="btn btn-sm btn-danger delete-confirm" data-item-name="الغرفة {{ room.number }}">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">لا توجد غرف</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
