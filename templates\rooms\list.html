{% extends 'base.html' %}

{% block title %}قائمة الغرف - نظام إدارة الفندق{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>قائمة الغرف</h2>
            <p class="text-muted">إدارة غرف الفندق وحالاتها</p>
        </div>
        <div>
            <a href="{% url 'rooms:types' %}" class="btn btn-outline-primary me-2">
                <i class="fas fa-list me-2"></i>
                أنواع الغرف
            </a>
            <a href="{% url 'rooms:add' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة غرفة جديدة
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ stats.total }}</div>
                        <div class="stats-label">إجمالي الغرف</div>
                    </div>
                    <div class="icon-md">
                        <i class="fas fa-bed"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card success">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ stats.available }}</div>
                        <div class="stats-label">متاحة</div>
                    </div>
                    <div class="icon-md">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card danger">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ stats.occupied }}</div>
                        <div class="stats-label">مشغولة</div>
                    </div>
                    <div class="icon-md">
                        <i class="fas fa-user-friends"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card warning">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="stats-number">{{ stats.maintenance }}</div>
                        <div class="stats-label">صيانة</div>
                    </div>
                    <div class="icon-md">
                        <i class="fas fa-tools"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج البحث والتصفية -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>
                البحث والتصفية
            </h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    {{ search_form.search }}
                </div>
                <div class="col-md-2">
                    {{ search_form.room_type }}
                </div>
                <div class="col-md-2">
                    {{ search_form.status }}
                </div>
                <div class="col-md-2">
                    {{ search_form.floor }}
                </div>
                <div class="col-md-2">
                    {{ search_form.is_active }}
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الغرف -->
    <div class="card">
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الغرفة</th>
                                <th>نوع الغرفة</th>
                                <th>الطابق</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>نشطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for room in page_obj %}
                            <tr>
                                <td>
                                    <strong>{{ room.number }}</strong>
                                </td>
                                <td>{{ room.room_type.name }}</td>
                                <td>
                                    <span class="badge bg-secondary">الطابق {{ room.floor }}</span>
                                </td>
                                <td>{{ room.price_per_night|floatformat:2 }} ريال</td>
                                <td>
                                    <select class="form-select form-select-sm room-status-update"
                                            data-room-id="{{ room.id }}"
                                            style="width: auto;">
                                        {% for status_code, status_name in room.ROOM_STATUS %}
                                            <option value="{{ status_code }}"
                                                    {% if room.status == status_code %}selected{% endif %}>
                                                {{ status_name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td>
                                    {% if room.is_active %}
                                        <span class="badge bg-success">نشطة</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشطة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'rooms:edit' room.pk %}"
                                           class="btn btn-sm btn-outline-warning"
                                           title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'rooms:delete' room.pk %}"
                                           class="btn btn-sm btn-outline-danger delete-confirm"
                                           data-item-name="الغرفة {{ room.number }}"
                                           title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- ترقيم الصفحات -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="ترقيم الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-bed fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد غرف</h5>
                    <p class="text-muted">ابدأ بإضافة غرف جديدة للفندق</p>
                    <a href="{% url 'rooms:add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة غرفة جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- حاوي الرسائل -->
<div id="alerts-container"></div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث حالة الغرفة
    $('.room-status-update').on('change', function() {
        var roomId = $(this).data('room-id');
        var newStatus = $(this).val();
        var selectElement = $(this);

        $.ajax({
            url: '{% url "rooms:update_status" %}',
            method: 'POST',
            data: {
                'room_id': roomId,
                'status': newStatus,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    showAlert(response.message, 'success');
                } else {
                    showAlert(response.message || 'حدث خطأ في تحديث حالة الغرفة', 'danger');
                    // إعادة تعيين القيمة السابقة
                    selectElement.val(selectElement.data('original-value'));
                }
            },
            error: function() {
                showAlert('حدث خطأ في الاتصال', 'danger');
                selectElement.val(selectElement.data('original-value'));
            }
        });
    });

    // حفظ القيمة الأصلية
    $('.room-status-update').each(function() {
        $(this).data('original-value', $(this).val());
    });
});
</script>
{% endblock %}
