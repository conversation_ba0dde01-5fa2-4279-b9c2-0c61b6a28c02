{% extends 'base.html' %}

{% block title %}إضافة غرفة - نظام إدارة الفندق{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>إضافة غرفة جديدة</h2>
                    <p class="text-muted">إضافة غرفة جديدة إلى الفندق</p>
                </div>
                <a href="{% url 'rooms:list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة إلى قائمة الغرف
                </a>
            </div>

            <div class="card shadow-custom">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        بيانات الغرفة الجديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}

                        <div class="row">
                            <!-- رقم الغرفة -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.number.id_for_label }}" class="form-label">
                                    <i class="fas fa-door-open me-1"></i>
                                    {{ form.number.label }}
                                </label>
                                {{ form.number }}
                                {% if form.number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.number.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">مثال: 101، 201، 301</div>
                            </div>

                            <!-- نوع الغرفة -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.room_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-bed me-1"></i>
                                    {{ form.room_type.label }}
                                </label>
                                {{ form.room_type }}
                                {% if form.room_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.room_type.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- الطابق -->
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.floor.id_for_label }}" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    {{ form.floor.label }}
                                </label>
                                {{ form.floor }}
                                {% if form.floor.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.floor.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- حالة الغرفة -->
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {{ form.status.label }}
                                </label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.status.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- سعر الليلة -->
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.price_per_night.id_for_label }}" class="form-label">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    {{ form.price_per_night.label }}
                                </label>
                                <div class="input-group">
                                    {{ form.price_per_night }}
                                    <span class="input-group-text">ريال</span>
                                </div>
                                {% if form.price_per_night.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.price_per_night.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- الحالة النشطة -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.is_active.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">إلغاء التحديد يعني أن الغرفة غير متاحة للحجز</div>
                            </div>
                        </div>

                        <!-- الملاحظات -->
                        <div class="row">
                            <div class="col-12 mb-4">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    {{ form.notes.label }}
                                </label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.notes.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'rooms:list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ الغرفة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث السعر تلقائياً عند تغيير نوع الغرفة
    $('#{{ form.room_type.id_for_label }}').on('change', function() {
        var roomTypeId = $(this).val();
        if (roomTypeId) {
            // يمكن إضافة AJAX call هنا لجلب السعر الافتراضي
            // من نوع الغرفة المحدد
        }
    });

    // التحقق من صحة النموذج
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
{% endblock %}
