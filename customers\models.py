from django.db import models
from django.core.validators import RegexValidator

class Customer(models.Model):
    """نموذج العملاء"""
    GENDER_CHOICES = (
        ('M', 'ذكر'),
        ('F', 'أنثى'),
    )

    ID_TYPE_CHOICES = (
        ('national_id', 'هوية وطنية'),
        ('passport', 'جواز سفر'),
        ('driving_license', 'رخصة قيادة'),
    )

    first_name = models.CharField(
        max_length=50,
        verbose_name='الاسم الأول'
    )
    last_name = models.CharField(
        max_length=50,
        verbose_name='اسم العائلة'
    )
    email = models.EmailField(
        unique=True,
        verbose_name='البريد الإلكتروني'
    )
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="رقم الهاتف يجب أن يكون بالصيغة: '+*********'. حتى 15 رقم مسموح."
    )
    phone = models.CharField(
        validators=[phone_regex],
        max_length=17,
        verbose_name='رقم الهاتف'
    )
    gender = models.CharField(
        max_length=1,
        choices=GENDER_CHOICES,
        verbose_name='الجنس'
    )
    date_of_birth = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ الميلاد'
    )
    id_type = models.CharField(
        max_length=20,
        choices=ID_TYPE_CHOICES,
        default='national_id',
        verbose_name='نوع الهوية'
    )
    id_number = models.CharField(
        max_length=50,
        verbose_name='رقم الهوية'
    )
    nationality = models.CharField(
        max_length=50,
        default='سعودي',
        verbose_name='الجنسية'
    )
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name='العنوان'
    )
    emergency_contact_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='اسم جهة الاتصال الطارئ'
    )
    emergency_contact_phone = models.CharField(
        max_length=17,
        blank=True,
        null=True,
        verbose_name='هاتف جهة الاتصال الطارئ'
    )
    is_vip = models.BooleanField(
        default=False,
        verbose_name='عميل مميز'
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'عميل'
        verbose_name_plural = 'العملاء'
        unique_together = ['id_type', 'id_number']

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
