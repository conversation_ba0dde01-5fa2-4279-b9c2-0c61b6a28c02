# Generated by Django 5.2.2 on 2025-06-09 23:09

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RoomType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='اسم نوع الغرفة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='السعر الأساسي')),
                ('max_occupancy', models.PositiveIntegerField(default=2, verbose_name='الحد الأقصى للإشغال')),
                ('amenities', models.TextField(blank=True, null=True, verbose_name='المرافق')),
            ],
            options={
                'verbose_name': 'نوع غرفة',
                'verbose_name_plural': 'أنواع الغرف',
            },
        ),
        migrations.CreateModel(
            name='Room',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=10, unique=True, verbose_name='رقم الغرفة')),
                ('floor', models.PositiveIntegerField(verbose_name='الطابق')),
                ('status', models.CharField(choices=[('available', 'متاحة'), ('occupied', 'مشغولة'), ('maintenance', 'صيانة'), ('cleaning', 'تنظيف')], default='available', max_length=20, verbose_name='حالة الغرفة')),
                ('price_per_night', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر الليلة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('room_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rooms.roomtype', verbose_name='نوع الغرفة')),
            ],
            options={
                'verbose_name': 'غرفة',
                'verbose_name_plural': 'الغرف',
                'ordering': ['number'],
            },
        ),
    ]
