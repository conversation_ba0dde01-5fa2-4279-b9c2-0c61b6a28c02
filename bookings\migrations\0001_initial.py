# Generated by Django 5.2.2 on 2025-06-09 23:09

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
        ('rooms', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Booking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('booking_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الحجز')),
                ('check_in_date', models.DateField(verbose_name='تاريخ الوصول')),
                ('check_out_date', models.DateField(verbose_name='تاريخ المغادرة')),
                ('actual_check_in', models.DateTimeField(blank=True, null=True, verbose_name='وقت الوصول الفعلي')),
                ('actual_check_out', models.DateTimeField(blank=True, null=True, verbose_name='وقت المغادرة الفعلي')),
                ('adults', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)], verbose_name='عدد البالغين')),
                ('children', models.PositiveIntegerField(default=0, verbose_name='عدد الأطفال')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('confirmed', 'مؤكد'), ('checked_in', 'تم تسجيل الوصول'), ('checked_out', 'تم تسجيل المغادرة'), ('cancelled', 'ملغي'), ('no_show', 'لم يحضر')], default='pending', max_length=20, verbose_name='حالة الحجز')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ الإجمالي')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ المدفوع')),
                ('special_requests', models.TextField(blank=True, null=True, verbose_name='طلبات خاصة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customers.customer', verbose_name='العميل')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rooms.room', verbose_name='الغرفة')),
            ],
            options={
                'verbose_name': 'حجز',
                'verbose_name_plural': 'الحجوزات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('credit_card', 'بطاقة ائتمان'), ('debit_card', 'بطاقة خصم'), ('bank_transfer', 'تحويل بنكي'), ('online', 'دفع إلكتروني')], max_length=20, verbose_name='طريقة الدفع')),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المعاملة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الدفع')),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='bookings.booking', verbose_name='الحجز')),
                ('processed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'المدفوعات',
                'ordering': ['-created_at'],
            },
        ),
    ]
